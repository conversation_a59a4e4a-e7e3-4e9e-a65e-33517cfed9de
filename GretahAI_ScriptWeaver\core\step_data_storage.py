"""
Persistent Step Data Storage for GretahAI ScriptWeaver

This module provides persistent JSON-based storage for test case step data,
ensuring hybrid-edited steps remain the authoritative source across all stages
and application restarts.

Key Features:
- JSON file-based persistence for step tables
- Single file per test case approach (no timestamped versions)
- Automatic synchronization with hybrid editing operations
- Data consistency validation and conflict resolution
- Comprehensive logging for debugging data flow issues
- Thread-safe file operations with atomic updates
- URL tracking integration for test execution history
- Automatic migration from legacy timestamped files

File Naming Convention:
- step_data_{test_case_id}.json (single file per test case)
- Backward compatibility with timestamped files: step_data_{test_case_id}_{timestamp}.json
"""

import os
import json
import threading
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
from debug_utils import debug

# Handle fcntl import for cross-platform compatibility
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    # fcntl is not available on Windows
    HAS_FCNTL = False

# Configuration
STEP_DATA_DIR = "step_data_storage"
FILE_LOCK_TIMEOUT = 30.0  # seconds

# Thread-local storage for file locks
thread_local = threading.local()


class StepDataStorage:
    """
    Persistent storage manager for test case step data.

    Provides methods to save, retrieve, and manage step table data across
    application sessions using JSON files with proper versioning and validation.
    """

    def __init__(self, storage_dir: str = None):
        """
        Initialize the step data storage.

        Args:
            storage_dir: Directory for storing step data files. If None, uses default.
        """
        if storage_dir is None:
            # Use default directory in the application root
            app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            storage_dir = os.path.join(app_dir, STEP_DATA_DIR)

        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)

        debug(f"StepDataStorage initialized with directory: {self.storage_dir}",
              stage="step_data_storage", operation="initialization",
              context={"storage_dir": str(self.storage_dir)})

    def _generate_filename(self, test_case_id: str, use_timestamp: bool = False) -> str:
        """
        Generate a filename for step data storage.

        Args:
            test_case_id: ID of the test case
            use_timestamp: If True, generates timestamped filename for backup. If False, uses simple filename.

        Returns:
            str: Generated filename
        """
        # Sanitize test case ID for filename
        safe_id = "".join(c for c in test_case_id if c.isalnum() or c in ('-', '_')).rstrip()

        if use_timestamp:
            # Generate timestamped filename for backup purposes
            timestamp = datetime.now()
            timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S_%f")
            return f"step_data_{safe_id}_{timestamp_str}.json"
        else:
            # Generate simple filename for single-file-per-test-case approach
            return f"step_data_{safe_id}.json"

    def _get_latest_file(self, test_case_id: str) -> Optional[Path]:
        """
        Get the step data file for a test case.
        Prioritizes single file approach, falls back to timestamped files for backward compatibility.

        Args:
            test_case_id: ID of the test case

        Returns:
            Path to the file or None if not found
        """
        safe_id = "".join(c for c in test_case_id if c.isalnum() or c in ('-', '_')).rstrip()

        # First, check for the single file approach
        single_file = self.storage_dir / f"step_data_{safe_id}.json"
        if single_file.exists():
            return single_file

        # Fall back to timestamped files for backward compatibility
        pattern = f"step_data_{safe_id}_*.json"
        matching_files = list(self.storage_dir.glob(pattern))
        if not matching_files:
            return None

        # Sort by modification time, newest first
        matching_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        return matching_files[0]

    def _calculate_data_hash(self, step_data: List[Dict[str, Any]]) -> str:
        """
        Calculate a hash of the step data for integrity checking.

        Args:
            step_data: List of step dictionaries

        Returns:
            str: SHA256 hash of the data
        """
        # Create a normalized JSON string for hashing
        normalized_json = json.dumps(step_data, sort_keys=True, separators=(',', ':'))
        return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()

    def save_step_data(self, test_case_id: str, step_data: List[Dict[str, Any]],
                      metadata: Dict[str, Any] = None) -> bool:
        """
        Save step data to a JSON file with metadata.
        Uses single file per test case approach for cleaner storage.

        Args:
            test_case_id: ID of the test case
            step_data: List of step dictionaries to save
            metadata: Additional metadata about the step data

        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            timestamp = datetime.now()
            filename = self._generate_filename(test_case_id, use_timestamp=False)
            file_path = self.storage_dir / filename

            # Ensure URL tracking fields are initialized for each step
            enhanced_step_data = []
            for step in step_data:
                enhanced_step = step.copy()

                # Initialize URL tracking fields if not present
                if 'current_url' not in enhanced_step:
                    enhanced_step['current_url'] = None
                if 'url_history' not in enhanced_step:
                    enhanced_step['url_history'] = []
                if 'url_capture_timestamp' not in enhanced_step:
                    enhanced_step['url_capture_timestamp'] = None
                if 'step_execution_urls' not in enhanced_step:
                    enhanced_step['step_execution_urls'] = {
                        'start_url': None,
                        'end_url': None,
                        'intermediate_urls': []
                    }

                enhanced_step_data.append(enhanced_step)

            # Prepare data structure
            data_to_save = {
                "test_case_id": test_case_id,
                "timestamp": timestamp.isoformat(),
                "data_hash": self._calculate_data_hash(enhanced_step_data),
                "step_count": len(enhanced_step_data),
                "metadata": metadata or {},
                "step_data": enhanced_step_data
            }

            # Write to temporary file first, then rename for atomic operation
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json',
                                           dir=self.storage_dir, delete=False) as temp_file:
                json.dump(data_to_save, temp_file, indent=2, ensure_ascii=False)
                temp_path = temp_file.name

            # Atomic rename (handle Windows file replacement)
            try:
                # On Windows, we need to remove the target file first if it exists
                if file_path.exists():
                    file_path.unlink()
                os.rename(temp_path, file_path)
            except Exception as rename_error:
                # Clean up temp file if rename fails
                try:
                    os.unlink(temp_path)
                except:
                    pass
                raise rename_error

            debug(f"Saved step data for test case {test_case_id}: {filename}",
                  stage="step_data_storage", operation="data_saved",
                  context={"test_case_id": test_case_id, "filename": filename,
                          "step_count": len(enhanced_step_data),
                          "data_hash": data_to_save['data_hash'][:16],
                          "file_path": str(file_path)})

            return True

        except Exception as e:
            debug(f"Failed to save step data for test case {test_case_id}: {e}",
                  stage="step_data_storage", operation="save_error",
                  context={"error": str(e), "test_case_id": test_case_id})
            return False

    def load_step_data(self, test_case_id: str) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        Load the latest step data for a test case.

        Args:
            test_case_id: ID of the test case

        Returns:
            Tuple of (step_data, metadata) or None if not found
        """
        try:
            latest_file = self._get_latest_file(test_case_id)
            if not latest_file:
                debug(f"No step data file found for test case: {test_case_id}",
                      stage="step_data_storage", operation="file_not_found",
                      context={"test_case_id": test_case_id})
                return None

            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Validate data structure
            required_fields = ['test_case_id', 'timestamp', 'step_data']
            for field in required_fields:
                if field not in data:
                    debug(f"Invalid step data file {latest_file}: missing field '{field}'",
                          stage="step_data_storage", operation="validation_error",
                          context={"file": str(latest_file), "missing_field": field, "test_case_id": test_case_id})
                    return None

            # Ensure step descriptions exist for all steps (handle legacy data)
            step_data = data['step_data']
            if isinstance(step_data, list):
                from core.ai import ensure_step_descriptions
                step_data = ensure_step_descriptions(step_data)
                data['step_data'] = step_data

            step_data = data['step_data']
            metadata = data.get('metadata', {})

            # Validate data integrity if hash is available
            if 'data_hash' in data:
                calculated_hash = self._calculate_data_hash(step_data)
                stored_hash = data['data_hash']
                if calculated_hash != stored_hash:
                    debug(f"Data integrity check failed for {latest_file}",
                          stage="step_data_storage", operation="integrity_check_failed",
                          context={"file": str(latest_file), "stored_hash": stored_hash,
                                  "calculated_hash": calculated_hash, "test_case_id": test_case_id})
                    # Continue anyway, but log the issue

            debug(f"Loaded step data for test case {test_case_id}: {latest_file.name}",
                  stage="step_data_storage", operation="data_loaded",
                  context={"test_case_id": test_case_id, "filename": latest_file.name,
                          "step_count": len(step_data), "timestamp": data.get('timestamp', 'unknown')})

            return step_data, metadata

        except Exception as e:
            debug(f"Failed to load step data for test case {test_case_id}: {e}",
                  stage="step_data_storage", operation="load_error",
                  context={"error": str(e), "test_case_id": test_case_id})
            return None

    def update_step_url_tracking(self, test_case_id: str, step_no: str, url_data: Dict[str, Any]) -> bool:
        """
        Update URL tracking information for a specific step.

        Args:
            test_case_id: ID of the test case
            step_no: Step number to update
            url_data: Dictionary containing URL tracking information

        Returns:
            bool: True if updated successfully, False otherwise
        """
        try:
            # Load current step data
            step_data_result = self.load_step_data(test_case_id)
            if not step_data_result:
                debug(f"No step data found for test case {test_case_id}",
                      stage="step_data_storage", operation="url_tracking_no_data",
                      context={"test_case_id": test_case_id})
                return False

            step_data, metadata = step_data_result

            # Find and update the specific step
            step_updated = False
            for step in step_data:
                if str(step.get('step_no', '')) == str(step_no):
                    # Update URL tracking fields
                    if 'current_url' in url_data:
                        step['current_url'] = url_data['current_url']
                        step['url_capture_timestamp'] = datetime.now().isoformat()

                    if 'url_history' in url_data:
                        if 'url_history' not in step:
                            step['url_history'] = []
                        step['url_history'].extend(url_data['url_history'])

                    if 'step_execution_urls' in url_data:
                        if 'step_execution_urls' not in step:
                            step['step_execution_urls'] = {
                                'start_url': None,
                                'end_url': None,
                                'intermediate_urls': []
                            }
                        step['step_execution_urls'].update(url_data['step_execution_urls'])

                    step_updated = True
                    debug(f"Updated URL tracking for step {step_no} in test case {test_case_id}",
                          stage="step_data_storage", operation="url_tracking_updated",
                          context={"test_case_id": test_case_id, "step_no": step_no})
                    break

            if not step_updated:
                debug(f"Step {step_no} not found in test case {test_case_id}",
                      stage="step_data_storage", operation="step_not_found",
                      context={"test_case_id": test_case_id, "step_no": step_no})
                return False

            # Save the updated step data
            metadata['url_tracking_update'] = {
                'step_no': step_no,
                'timestamp': datetime.now().isoformat(),
                'operation': 'url_tracking_update'
            }

            return self.save_step_data(test_case_id, step_data, metadata)

        except Exception as e:
            debug(f"Failed to update URL tracking for step {step_no} in test case {test_case_id}: {e}",
                  stage="step_data_storage", operation="url_tracking_error",
                  context={"error": str(e), "test_case_id": test_case_id, "step_no": step_no})
            return False

    def get_step_url_history(self, test_case_id: str, step_no: str = None) -> Dict[str, Any]:
        """
        Get URL history for a specific step or all steps.

        Args:
            test_case_id: ID of the test case
            step_no: Optional step number to get history for (if None, returns all steps)

        Returns:
            Dict containing URL history information
        """
        try:
            step_data_result = self.load_step_data(test_case_id)
            if not step_data_result:
                return {}

            step_data, _ = step_data_result
            url_history = {}

            for step in step_data:
                current_step_no = str(step.get('step_no', ''))

                if step_no is None or current_step_no == str(step_no):
                    url_history[current_step_no] = {
                        'current_url': step.get('current_url'),
                        'url_history': step.get('url_history', []),
                        'url_capture_timestamp': step.get('url_capture_timestamp'),
                        'step_execution_urls': step.get('step_execution_urls', {})
                    }

            return url_history

        except Exception as e:
            debug(f"Failed to get URL history for test case {test_case_id}: {e}",
                  stage="step_data_storage", operation="url_history_error",
                  context={"error": str(e), "test_case_id": test_case_id})
            return {}

    def migrate_to_single_file(self, test_case_id: str) -> bool:
        """
        Migrate from timestamped files to single file approach for a test case.

        Args:
            test_case_id: ID of the test case

        Returns:
            bool: True if migration was successful or not needed, False otherwise
        """
        try:
            safe_id = "".join(c for c in test_case_id if c.isalnum() or c in ('-', '_')).rstrip()
            single_file = self.storage_dir / f"step_data_{safe_id}.json"

            # If single file already exists, no migration needed
            if single_file.exists():
                debug(f"Single file already exists for test case {test_case_id}, no migration needed",
                      stage="step_data_storage", operation="migration_not_needed",
                      context={"test_case_id": test_case_id, "reason": "single_file_exists"})
                return True

            # Find the latest timestamped file
            pattern = f"step_data_{safe_id}_*.json"
            matching_files = list(self.storage_dir.glob(pattern))

            if not matching_files:
                debug(f"No timestamped files found for test case {test_case_id}, no migration needed",
                      stage="step_data_storage", operation="migration_not_needed",
                      context={"test_case_id": test_case_id, "reason": "no_timestamped_files"})
                return True

            # Get the latest timestamped file
            latest_timestamped_file = max(matching_files, key=lambda f: f.stat().st_mtime)

            # Load data from the latest timestamped file
            step_data_result = self.load_step_data(test_case_id)
            if not step_data_result:
                debug(f"Failed to load data from timestamped file for migration: {test_case_id}",
                      stage="step_data_storage", operation="migration_load_error",
                      context={"test_case_id": test_case_id})
                return False

            step_data, metadata = step_data_result

            # Save to single file format
            success = self.save_step_data(test_case_id, step_data, metadata)

            if success:
                debug(f"Successfully migrated test case {test_case_id} to single file format",
                      stage="step_data_storage", operation="migration_completed",
                      context={"test_case_id": test_case_id,
                              "from_file": latest_timestamped_file.name,
                              "to_file": single_file.name})

                # Optionally clean up old timestamped files after successful migration
                # Uncomment the following lines if you want to remove old files after migration
                # for old_file in matching_files:
                #     try:
                #         old_file.unlink()
                #         debug(f"Removed old timestamped file: {old_file.name}",
                #               stage="step_data_storage", operation="cleanup_file",
                #               context={"test_case_id": test_case_id, "file": old_file.name})
                #     except Exception as e:
                #         debug(f"Failed to remove old file {old_file}: {e}",
                #               stage="step_data_storage", operation="cleanup_error",
                #               context={"test_case_id": test_case_id, "file": str(old_file), "error": str(e)})

                return True
            else:
                debug(f"Failed to save migrated data for test case {test_case_id}",
                      stage="step_data_storage", operation="migration_save_error",
                      context={"test_case_id": test_case_id})
                return False

        except Exception as e:
            debug(f"Failed to migrate test case {test_case_id} to single file: {e}",
                  stage="step_data_storage", operation="migration_error",
                  context={"error": str(e), "test_case_id": test_case_id})
            return False

    def get_latest_file_path(self, test_case_id: str) -> Optional[Path]:
        """
        Get the path to the step data file for a test case.
        Uses single file approach, with automatic migration from timestamped files.

        Args:
            test_case_id: ID of the test case

        Returns:
            Optional[Path]: Path to the file, or None if not found
        """
        try:
            # Try to migrate to single file if needed
            self.migrate_to_single_file(test_case_id)

            # Return the current file path
            return self._get_latest_file(test_case_id)

        except Exception as e:
            logger.error(f"Failed to get file path for test case {test_case_id}: {e}")
            return None


# Global storage instance
_step_storage_instance = None
_storage_lock = threading.Lock()


def get_step_data_storage() -> StepDataStorage:
    """
    Get the global step data storage instance.

    Returns:
        StepDataStorage: The global storage instance
    """
    global _step_storage_instance

    if _step_storage_instance is None:
        with _storage_lock:
            if _step_storage_instance is None:
                _step_storage_instance = StepDataStorage()

    return _step_storage_instance
